import React, { useState, useEffect } from 'react';
import { TariffUpload, TariffPriceData, MealPlan, ExtractedTariffData } from '@/types/types';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Check, X, Calendar, FileText, AlertCircle, Edit2, Save, Clock } from 'lucide-react';
import { updateTariffStatus } from '@/utils/api-functions/tariff-upload';
import { handleMealPlanApi } from '@/utils/api-functions/addMealPlan';
import { MealPlanData } from '@/components/page-components/hotel-details/room/mealPlan/AddMealPlan';
import { filterRoomsByName } from '@/utils/roomNameMatcher';
import toast from 'react-hot-toast';
import { getTariffCacheInfo, getCacheRemainingTimeFormatted } from '@/utils/cache/tariff-cache';

interface TariffComparisonProps {
  tariff: TariffUpload;
  existingData: MealPlan[];
  extractedData?: ExtractedTariffData[];
  roomName: string;
  onApprove: () => void;
  onReject: () => void;
  onClose: () => void;
}

const TariffComparison: React.FC<TariffComparisonProps> = ({
  tariff,
  existingData,
  extractedData = [],
  roomName,
  onApprove,
  onReject,
  onClose
}) => {
  const [finalData, setFinalData] = useState<TariffPriceData[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({});
  const [isProcessing, setIsProcessing] = useState(false);

  // State for editing functionality
  const [editingItems, setEditingItems] = useState<Record<string, boolean>>({});
  const [editedValues, setEditedValues] = useState<Record<string, { roomPrice: number; adultPrice: number; childPrice: number; gstAmount: number; startDate: string; endDate: string }>>({});

  // Utility function to convert ExtractedTariffData to TariffPriceData
  const convertExtractedToTariffData = (extracted: ExtractedTariffData): TariffPriceData => {
    // Safety check
    if (!extracted) {
      console.warn('convertExtractedToTariffData: received undefined/null extracted data');
      return {
        mealPlanType: 'Unknown',
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        roomPrice: 0,
        season: 'Unknown'
      };
    }

    // Handle both LLM and fallback formats
    const mealPlan = extracted['Plan'] || extracted['Meal Plan'] || '';

    // Correct price mapping based on backend extraction structure
    // LLM path has separate Room Price, Adult Price, Child Price fields
    // Fallback path has a single Price field that should be used as room price
    const roomPrice = parseFloat(String(extracted['Room Price'] || extracted['Price'] || 0)) || 0;
    const adultPrice = parseFloat(String(extracted['Adult Price'] || 0)) || 0;
    const childPrice = parseFloat(String(extracted['Child Price'] || 0)) || 0;
    const gstAmount = parseFloat(String(extracted['GST Amount'] || 0)) || 0;

    // Get season information
    const season = extracted['Season'] || 'Unknown';

    // Generate default date ranges based on season
    /*
    const generateSeasonDates = (seasonType: string): { startDate: string; endDate: string } => {
      const currentYear = new Date().getFullYear();

      switch (seasonType.toLowerCase()) {
        case 'peakseason':
        case 'peak':
          return {
            startDate: `${currentYear}-12-15T00:00:00.000Z`,
            endDate: `${currentYear + 1}-01-15T00:00:00.000Z`
          };
        case 'shoulderseason':
        case 'shoulder':
          return {
            startDate: `${currentYear}-03-01T00:00:00.000Z`,
            endDate: `${currentYear}-05-31T00:00:00.000Z`
          };
        case 'offseason':
        case 'off':
          return {
            startDate: `${currentYear}-06-01T00:00:00.000Z`,
            endDate: `${currentYear}-09-30T00:00:00.000Z`
          };
        case 'blackout':
          return {
            startDate: `${currentYear}-04-01T00:00:00.000Z`,
            endDate: `${currentYear}-04-30T00:00:00.000Z`
          };
        default:
          return {
            startDate: `${currentYear}-01-01T00:00:00.000Z`,
            endDate: `${currentYear}-12-31T00:00:00.000Z`
          };
      }
    };
    */

    // Try to use actual dates from extracted data - no fallback to season dates
    const extractedStartDate = extracted['Start Date'] || (extracted as any)['start_date'];
    const extractedEndDate = extracted['End Date'] || (extracted as any)['end_date'];

    // Parse dates - handle different formats
    const parseDate = (dateStr: string): string | null => {
      console.log('Parsing date:', dateStr);
      if (!dateStr) return null;

      // If already in ISO format, return as is
      if (dateStr.includes('T') || dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00.000Z`;
      }

      // Handle formats like "01 Oct", "30 Jun", "15 Apr", etc.
      const currentYear = new Date().getFullYear();
      const monthMap: Record<string, string> = {
        'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
        'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
        'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
      };

      // Handle "DD MMM" format (e.g., "01 Oct", "30 Jun", "31 Mar")
      const ddMmmMatch = dateStr.match(/(\d{1,2})\s+([A-Za-z]{3,})/);
      if (ddMmmMatch) {
        const day = ddMmmMatch[1].padStart(2, '0');
        let monthAbbr = ddMmmMatch[2].toUpperCase();

        // Handle common month abbreviations that might be truncated
        const monthAbbreviations: Record<string, string> = {
          'JAN': '01', 'JANUARY': '01',
          'FEB': '02', 'FEBRUARY': '02',
          'MAR': '03', 'MARCH': '03',
          'APR': '04', 'APRIL': '04',
          'MAY': '05',
          'JUN': '06', 'JUNE': '06',
          'JUL': '07', 'JULY': '07',
          'AUG': '08', 'AUGUST': '08',
          'SEP': '09', 'SEPT': '09', 'SEPTEMBER': '09',
          'OCT': '10', 'OCTOBER': '10',
          'NOV': '11', 'NOVEMBER': '11',
          'DEC': '12', 'DECEMBER': '12'
        };

        const month = monthAbbreviations[monthAbbr] || '01';

        // Use current year for all dates - let users edit if needed
        let year = currentYear;

        const finalDate = `${year}-${month}-${day}T00:00:00.000Z`;
        console.log(`Parsed "${dateStr}" -> ${finalDate}`);
        return finalDate;
      }

      // Handle "DD-MMM" format (e.g., "15-APR", "1-JUN")
      const ddDashMmmMatch = dateStr.match(/(\d{1,2})-([A-Z]{3})/);
      if (ddDashMmmMatch) {
        const day = ddDashMmmMatch[1].padStart(2, '0');
        const month = monthMap[ddDashMmmMatch[2]] || '01';
        return `${currentYear}-${month}-${day}T00:00:00.000Z`;
      }

      // Fallback: try to parse as regular date
      try {
        const parsedDate = new Date(dateStr);
        // If the parsed date is valid and not in the past century, use it
        if (!isNaN(parsedDate.getTime()) && parsedDate.getFullYear() > 1900) {
          return parsedDate.toISOString();
        }
      } catch {
        // Continue to fallback
      }

      return null; // Return null instead of fallback date
    };
    
    // Use actual extracted dates if available, otherwise leave as null
    const startDate = extractedStartDate ? parseDate(extractedStartDate) : null;
    const endDate = extractedEndDate ? parseDate(extractedEndDate) : null;

    return {
      mealPlanType: mealPlan.toUpperCase(), // Normalize to uppercase for consistent comparison
      startDate: startDate,
      endDate: endDate,
      roomPrice: roomPrice,
      adultPrice: adultPrice,
      childPrice: childPrice,
      gstAmount: gstAmount,
      season: season
    };
  };

  // Convert extracted data to internal format and filter by room category
  const convertedExtractedData: TariffPriceData[] = React.useMemo(() => {
    // Filter extracted data to only include prices for the selected room category
    const filteredData = extractedData.filter(item => {
      const roomCategory = item['Room Category'];
      if (!roomCategory) return true; // Include if no room category specified

      // Use improved fuzzy matching for room names
      return filterRoomsByName(roomCategory, roomName, 0.6);
    });

    const convertedData = filteredData.map(convertExtractedToTariffData).filter(item => item && item.mealPlanType);
    console.log(`Filtered ${filteredData.length} items for room "${roomName}" from ${extractedData.length} total items`);
    return convertedData;
  }, [extractedData, roomName]);

  // Convert existing data to TariffPriceData format for comparison
  const convertedExistingData: TariffPriceData[] = React.useMemo(() => {
    const result = existingData.flatMap(mealPlan => {
      const mealPlanResult: TariffPriceData[] = [];

      for (let i = 0; i < mealPlan.startDate.length; i++) {
        if (mealPlan.startDate[i] && mealPlan.endDate[i]) {
          const converted = {
            mealPlanType: mealPlan.mealPlan.toUpperCase(), // Normalize to uppercase for comparison
            startDate: mealPlan.startDate[i],
            endDate: mealPlan.endDate[i],
            roomPrice: mealPlan.roomPrice,
            adultPrice: mealPlan.adultPrice,
            childPrice: mealPlan.childPrice
          };
          mealPlanResult.push(converted);
        }
      }

      return mealPlanResult;
    });

    return result;
  }, [existingData]);

  // Initialize selected items (all false by default - user must manually select)
  useEffect(() => {
    const initialSelected: Record<string, boolean> = {};

    convertedExtractedData.forEach((_item, index) => {
      initialSelected[`item-${index}`] = false; // Changed to false - no items selected by default
    });

    setSelectedItems(initialSelected);
    console.log('Initialized selected items:', initialSelected); // Debug log
  }, [convertedExtractedData]);

  // Update final data when selections change or items are edited
  useEffect(() => {
    const newFinalData = convertedExtractedData
      .map((item, index) => {
        // Use index-based key for consistency
        const key = `item-${index}`;
        const editedValue = editedValues[key];

        // If item has been edited, use edited values
        if (editedValue) {
          return {
            ...item,
            roomPrice: editedValue.roomPrice,
            adultPrice: editedValue.adultPrice,
            childPrice: editedValue.childPrice,
            gstAmount: editedValue.gstAmount,
            startDate: new Date(editedValue.startDate).toISOString(),
            endDate: new Date(editedValue.endDate).toISOString()
          };
        }

        return item;
      })
      .filter((_item, index) =>
        selectedItems[`item-${index}`]
      );

    setFinalData(newFinalData);
    console.log('Final data updated:', newFinalData); // Debug log
    console.log('Selected items:', selectedItems); // Debug log
  }, [selectedItems, editedValues]); // Removed convertedExtractedData dependency to prevent resetting edited values

  // Handle changes to convertedExtractedData (like room selection changes) while preserving edited values
  useEffect(() => {
    // Only update final data if there are no edited values to preserve
    if (Object.keys(editedValues).length === 0) {
      const newFinalData = convertedExtractedData
        .filter((_item, index) =>
          selectedItems[`item-${index}`]
        );
      setFinalData(newFinalData);
    }
  }, [convertedExtractedData]);

  // Toggle selection of an item
  const toggleSelection = (_item: TariffPriceData, index: number) => {
    const key = `item-${index}`;
    setSelectedItems(prev => {
      const newSelection = {
        ...prev,
        [key]: !prev[key]
      };
      console.log('Toggled selection for', key, ':', !prev[key]); // Debug log
      console.log('New selection state:', newSelection); // Debug log
      return newSelection;
    });
  };

  // Start editing an item
  const startEditing = (item: TariffPriceData, index: number) => {
    const key = `item-${index}`;
    setEditingItems(prev => ({ ...prev, [key]: true }));
    setEditedValues(prev => ({
      ...prev,
      [key]: {
        roomPrice: item.roomPrice,
        adultPrice: item.adultPrice || 0,
        childPrice: item.childPrice || 0,
        gstAmount: item.gstAmount || 0,
        startDate: item.startDate ? item.startDate.split('T')[0] : '', // Handle null dates
        endDate: item.endDate ? item.endDate.split('T')[0] : ''
      }
    }));
  };

  // Save edited item
  const saveEdit = (_item: TariffPriceData, index: number) => {
    const key = `item-${index}`;
    const editedValue = editedValues[key];

    if (editedValue) {
      // Validate dates
      const startDate = new Date(editedValue.startDate);
      const endDate = new Date(editedValue.endDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        toast.error('Please enter valid dates');
        return;
      }

      if (startDate >= endDate) {
        toast.error('Start date must be before end date');
        return;
      }

      if (editedValue.roomPrice <= 0) {
        toast.error('Room price must be greater than 0');
        return;
      }

      if (editedValue.adultPrice < 0) {
        toast.error('Adult price cannot be negative');
        return;
      }

      if (editedValue.childPrice < 0) {
        toast.error('Child price cannot be negative');
        return;
      }

      if (editedValue.gstAmount < 0) {
        toast.error('GST amount cannot be negative');
        return;
      }

      // Exit editing mode but keep the edited values
      setEditingItems(prev => ({ ...prev, [key]: false }));

      console.log('Saved edited values for item', index, ':', editedValue); // Debug log
      console.log('Current edited values state:', editedValues); // Debug log
      console.log('Is item selected?', selectedItems[key]); // Debug log
      toast.success('Tariff data updated successfully');
    }
  };

  // Cancel editing
  const cancelEdit = (_item: TariffPriceData, index: number) => {
    const key = `item-${index}`;
    setEditingItems(prev => ({ ...prev, [key]: false }));
    setEditedValues(prev => {
      const newValues = { ...prev };
      delete newValues[key];
      return newValues;
    });
  };

  // Update edited value
  const updateEditedValue = (_item: TariffPriceData, index: number, field: string, value: string | number) => {
    const key = `item-${index}`;
    setEditedValues(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        [field]: value
      }
    }));
  };

  // Find conflicts between existing and extracted data
  const findConflict = (item: TariffPriceData): TariffPriceData | null => {
    const conflict = convertedExistingData.find(existing => {
      const mealPlanMatch = existing.mealPlanType === item.mealPlanType;

      // Skip date overlap check if either item has null dates
      if (!item.startDate || !item.endDate || !existing.startDate || !existing.endDate) {
        return mealPlanMatch; // Only check meal plan match if dates are missing
      }

      const dateOverlap = (new Date(existing.startDate) <= new Date(item.endDate) &&
                          new Date(existing.endDate) >= new Date(item.startDate));

      return mealPlanMatch && dateOverlap;
    });

    return conflict || null;
  };

  // Convert TariffPriceData to MealPlanData format
  const convertToMealPlanData = (tariffData: TariffPriceData[]): MealPlanData[] => {
    return tariffData
      .filter(item => item.startDate && item.endDate) // Only include items with valid dates
      .map(item => {
        // Calculate GST percentage if we have GST amount and room price
        let gstPercentage = 18; // Default GST percentage
        if (item.gstAmount && item.gstAmount > 0 && item.roomPrice > 0) {
          gstPercentage = Math.round((item.gstAmount / item.roomPrice) * 100);
        }

        return {
          mealPlan: item.mealPlanType.toLowerCase(), // Convert to lowercase as required by API
          roomPrice: item.roomPrice,
          adultPrice: item.adultPrice || 0, // Use extracted values or default to 0
          childPrice: item.childPrice || 0,
          seasonType: 'Regular', // Default season type
          startDate: [item.startDate!], // Non-null assertion since we filtered above
          endDate: [item.endDate!],
          gstPer: gstPercentage
        };
      });
  };

  // Handle approval with selected data
  const handleApprove = async () => {
    if (finalData.length === 0) {
      toast.error('No tariff data selected for approval');
      return;
    }

    // Check for items with missing dates
    const itemsWithMissingDates = finalData.filter(item => !item.startDate || !item.endDate);
    if (itemsWithMissingDates.length > 0) {
      toast.error(`${itemsWithMissingDates.length} item(s) have missing date ranges and will be skipped. Please set dates manually if needed.`);
    }

    setIsProcessing(true);

    try {
      // First, update the tariff status
      await updateTariffStatus(
        tariff.tariffId!,
        'approved',
        finalData,
        `Approved with ${finalData.length} price updates`
      );

      // Then, store the approved meal plans in the database
      const mealPlanData = convertToMealPlanData(finalData);

      try {
        await handleMealPlanApi(tariff.roomId, mealPlanData);
        toast.success(`Tariff approved and ${finalData.length} meal plans added to database`);
      } catch (mealPlanError) {
        console.warn('Tariff approved but failed to store meal plans:', mealPlanError);
        toast.success('Tariff approved, but there was an issue storing meal plans. Please add them manually if needed.');
      }

      onApprove();
    } catch (error) {
      console.error('Error approving tariff:', error);
      toast.error('Failed to approve tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle rejection
  const handleReject = async () => {
    setIsProcessing(true);

    try {
      await updateTariffStatus(
        tariff.tariffId!,
        'rejected',
        undefined,
        'Rejected by administrator'
      );

      toast.success('Tariff rejected');
      onReject();
    } catch (error) {
      console.error('Error rejecting tariff:', error);
      toast.error('Failed to reject tariff');
    } finally {
      setIsProcessing(false);
    }
  };

  // Group extracted data by meal plan type for easier comparison
  const groupedExtractedData: Record<string, { tariffData: TariffPriceData; originalData: ExtractedTariffData }[]> = React.useMemo(() => {
    const grouped: Record<string, { tariffData: TariffPriceData; originalData: ExtractedTariffData }[]> = {};

    // Filter the original extracted data to match the converted data
    const filteredOriginalData = extractedData.filter(item => {
      const roomCategory = item['Room Category'];
      if (!roomCategory) return true; // Include if no room category specified

      // Use improved fuzzy matching for room names
      return filterRoomsByName(roomCategory, roomName, 0.6);
    });

    // Group the converted data with their corresponding original data
    convertedExtractedData.forEach((convertedItem, index) => {
      const originalItem = filteredOriginalData[index];
      if (convertedItem && convertedItem.mealPlanType && originalItem) {
        if (!grouped[convertedItem.mealPlanType]) {
          grouped[convertedItem.mealPlanType] = [];
        }
        grouped[convertedItem.mealPlanType].push({
          tariffData: convertedItem,
          originalData: originalItem
        });
      }
    });
    return grouped;
  }, [extractedData, convertedExtractedData, roomName]);



  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-5xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">
            Tariff Comparison - {roomName}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Compare extracted tariff data with existing prices before approval
          </p>
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="flex items-center gap-2">
            <FileText className="text-blue-500" size={18} />
            <span className="text-sm text-gray-600">
              {tariff.filePath.split('-').pop()}
            </span>
          </div>
          {/* Cache Status */}
          {(() => {
            const cacheInfo = getTariffCacheInfo();
            return cacheInfo && cacheInfo.isValid ? (
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <Clock size={14} />
                <span>Cache expires in: {getCacheRemainingTimeFormatted()}</span>
              </div>
            ) : null;
          })()}
        </div>
      </div>

      {/* Alert if no data extracted */}
      {extractedData.length === 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
          <div className="flex">
            <AlertCircle className="text-amber-500 mr-3" size={20} />
            <div>
              <h3 className="text-sm font-medium text-amber-800">No Data Extracted</h3>
              <p className="text-sm text-amber-700 mt-1">
                No price data could be extracted from this PDF. Please review the format and try again.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Comparison content */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-700 mb-3">Price Comparison</h3>

        {/* Meal Plan Tabs */}
        <div className="mb-4 border-b">
          {Object.keys(groupedExtractedData).length === 0 && (
            <p className="text-gray-500 italic py-2">No meal plans found in uploaded tariff</p>
          )}

          {Object.keys(groupedExtractedData).map(mealPlanType => (
            <div key={mealPlanType} className="mb-6">
              <h4 className="text-md font-semibold bg-gray-50 p-3 border-l-4 border-blue-500">
                {mealPlanType} Plan
              </h4>

              <div className="mt-3 overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Select
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Season
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date Range
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Room Price (₹)
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Adult Price (₹)
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Child Price (₹)
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        GST (₹)
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Existing Price (₹)
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Change
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {groupedExtractedData[mealPlanType].map(({ tariffData: item, originalData }, index) => {
                      const conflict = findConflict(item);
                      const isSelected = selectedItems[`item-${index}`];
                      const priceChange = conflict ? item.roomPrice - conflict.roomPrice : item.roomPrice;

                      return (
                        <tr
                          key={`${item.startDate}-${item.endDate}-${index}`}
                          className={`${isSelected ? 'bg-blue-50' : 'bg-white'} hover:bg-gray-50`}
                        >
                          <td className="px-4 py-3">
                            <input
                              type="checkbox"
                              checked={!!isSelected}
                              onChange={() => toggleSelection(item, index)}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                          <td className="px-4 py-3 text-sm">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {originalData.Season}
                            </span>
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];
                              const editedValue = editedValues[key];

                              if (isEditing && editedValue) {
                                return (
                                  <div className="flex flex-col gap-1">
                                    <div className="flex items-center gap-1">
                                      <span className="text-xs text-gray-500 w-8">From:</span>
                                      <input
                                        type="date"
                                        value={editedValue.startDate}
                                        onChange={(e) => updateEditedValue(item, index, 'startDate', e.target.value)}
                                        className="px-2 py-1 border border-gray-300 rounded text-xs w-32"
                                      />
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <span className="text-xs text-gray-500 w-8">To:</span>
                                      <input
                                        type="date"
                                        value={editedValue.endDate}
                                        onChange={(e) => updateEditedValue(item, index, 'endDate', e.target.value)}
                                        className="px-2 py-1 border border-gray-300 rounded text-xs w-32"
                                      />
                                    </div>
                                  </div>
                                );
                              }

                              // Handle null dates - show message when dates are not available from PDF
                              if (!item.startDate || !item.endDate) {
                                return (
                                  <span className="text-gray-500 italic text-xs">
                                    Date range not found in PDF
                                  </span>
                                );
                              }

                              try {
                                const startDate = new Date(item.startDate);
                                const endDate = new Date(item.endDate);
                                return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
                              } catch (error) {
                                return (
                                  <span className="text-gray-500 italic text-xs">
                                    Invalid date format
                                  </span>
                                );
                              }
                            })()}
                          </td>
                          <td className="px-3 py-3 text-sm font-medium">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];
                              const editedValue = editedValues[key];

                              if (isEditing && editedValue) {
                                return (
                                  <input
                                    type="number"
                                    value={editedValue.roomPrice}
                                    onChange={(e) => updateEditedValue(item, index, 'roomPrice', parseFloat(e.target.value) || 0)}
                                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    min="0"
                                    step="0.01"
                                  />
                                );
                              }
                              return `₹${item.roomPrice.toFixed(2)}`;
                            })()}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];
                              const editedValue = editedValues[key];

                              if (isEditing && editedValue) {
                                return (
                                  <input
                                    type="number"
                                    value={editedValue.adultPrice}
                                    onChange={(e) => updateEditedValue(item, index, 'adultPrice', parseFloat(e.target.value) || 0)}
                                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    min="0"
                                    step="0.01"
                                  />
                                );
                              }
                              return <span className="text-gray-600">₹{(item.adultPrice || 0).toFixed(2)}</span>;
                            })()}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];
                              const editedValue = editedValues[key];

                              if (isEditing && editedValue) {
                                return (
                                  <input
                                    type="number"
                                    value={editedValue.childPrice}
                                    onChange={(e) => updateEditedValue(item, index, 'childPrice', parseFloat(e.target.value) || 0)}
                                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    min="0"
                                    step="0.01"
                                  />
                                );
                              }
                              return <span className="text-gray-600">₹{(item.childPrice || 0).toFixed(2)}</span>;
                            })()}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];
                              const editedValue = editedValues[key];

                              if (isEditing && editedValue) {
                                return (
                                  <input
                                    type="number"
                                    value={editedValue.gstAmount}
                                    onChange={(e) => updateEditedValue(item, index, 'gstAmount', parseFloat(e.target.value) || 0)}
                                    className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                                    min="0"
                                    step="0.01"
                                  />
                                );
                              }
                              return <span className="text-gray-600">₹{(item.gstAmount || 0).toFixed(2)}</span>;
                            })()}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {conflict ? (
                              <>₹{conflict.roomPrice.toFixed(2)}</>
                            ) : (
                              <span className="text-gray-400">No existing data</span>
                            )}
                          </td>
                          <td className="px-3 py-3 text-sm">
                            {(() => {
                              const key = `item-${index}`;
                              const isEditing = editingItems[key];

                              if (isEditing) {
                                return (
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      onClick={() => saveEdit(item, index)}
                                      className="h-6 w-6 p-0 bg-green-600 hover:bg-green-700"
                                    >
                                      <Save size={12} />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => cancelEdit(item, index)}
                                      className="h-6 w-6 p-0"
                                    >
                                      <X size={12} />
                                    </Button>
                                  </div>
                                );
                              }

                              if (conflict) {
                                return (
                                  <div className="flex items-center gap-2">
                                    <span className={`${priceChange > 0 ? 'text-green-600' : priceChange < 0 ? 'text-red-600' : 'text-gray-500'}`}>
                                      {priceChange > 0 ? '+' : ''}₹{Math.abs(priceChange).toFixed(2)}
                                    </span>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => startEditing(item, index)}
                                      className="h-6 w-6 p-0"
                                    >
                                      <Edit2 size={12} />
                                    </Button>
                                  </div>
                                );
                              }

                              return (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => startEditing(item, index)}
                                  className="h-7 px-2 text-xs"
                                >
                                  <Edit2 size={12} className="mr-1" />
                                  Edit
                                </Button>
                              );
                            })()}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Calendar View Button */}
      <div className="flex justify-center mb-6">
        <Button
          variant="outline"
          className="text-blue-700 border-blue-200 hover:bg-blue-50"
          onClick={() => window.open(`/hotels/edit/${tariff.hotelId}?view=calendar&room=${tariff.roomId}`, '_blank')}
        >
          <Calendar size={16} className="mr-2" />
          Open in Price Calendar
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 mt-6 pt-4 border-t">
        <Button
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
        >
          Cancel
        </Button>
        <Button
          variant="outline"
          onClick={handleReject}
          disabled={isProcessing}
          className="text-red-600 border-red-200 hover:bg-red-50"
        >
          <X size={16} className="mr-1.5" />
          Reject Tariff
        </Button>
        <Button
          onClick={handleApprove}
          disabled={finalData.length === 0 || isProcessing}
          className="bg-green-600 text-white hover:bg-green-700"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            <>
              <Check size={16} className="mr-1.5" />
              Approve Selected ({finalData.length})
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default TariffComparison;